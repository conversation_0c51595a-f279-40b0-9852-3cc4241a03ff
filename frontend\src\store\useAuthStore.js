import { create } from 'zustand';

export const useAuthStore = create((set) => ({
    authUser: null,
    isSigningUp: false,
    isLoggingIng: false,
    isUpdatingProfile: false,

    isCheckAuth:true,

    checkAuth:async () => {
        try {
            const res = await axiosInstance.get("/auth/check");
            set({ authUser: res.data });  
        } catch (error) {
            console.log(error);
            set({ authUser: null });   
        }finally{
            set({isCheckAuth:false})
        }
    },

    signup: async (data) => {
        
    }
    

}));